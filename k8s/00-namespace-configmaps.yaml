apiVersion: v1
kind: Namespace
metadata:
  name: prs-production
  labels:
    name: prs-production
    environment: production
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: prs-production
data:
  NODE_ENV: "production"
  PORT: "4000"
  HOST: "0.0.0.0"
  LOG_LEVEL: "info"
  ENABLE_REQUEST_LOGS: "true"
  DISABLE_DEBUG_ROUTES: "true"
  ENABLE_SWAGGER: "false"
  BYPASS_OTP: "false"
  SESSION_TIMEOUT: "3600"
  MAX_LOGIN_ATTEMPTS: "5"
  LOCKOUT_DURATION: "900"
  POSTGRES_HOST: "postgres-primary"
  POSTGRES_DB: "prs_production"
  POSTGRES_PORT: "5432"
  POSTGRES_USER: "prs_user"
  POSTGRES_SSL_MODE: "require"
  POOL_MIN: "5"
  POOL_MAX: "20"
  POOL_ACQUIRE: "60000"
  POOL_IDLE: "10000"
  POOL_EVICTION: "30000"
  REDIS_HOST: "redis-cluster"
  REDIS_PORT: "6379"
  REDIS_TLS_ENABLED: "true"
  REDIS_CLUSTER_ENABLED: "true"
  MINIO_ENDPOINT: "minio-cluster"
  MINIO_PORT: "9000"
  MINIO_USE_SSL: "true"
  MINIO_BUCKET: "prs-uploads-prod"
  CITYLAND_API_URL: "https://api.cityland.gov"
  CITYLAND_ACCOUNTING_URL: "https://accounting.cityland.gov"
  ASSOCIATION_DEPARTMENT_CODE: "10"
  RATE_LIMIT_WINDOW: "900000"
  RATE_LIMIT_MAX_REQUESTS: "100"
  RATE_LIMIT_SKIP_SUCCESSFUL: "true"
  CORS_ORIGIN: "https://your-domain.com,https://api.your-domain.com"
  CORS_CREDENTIALS: "true"
  PROMETHEUS_ENABLED: "true"
  LOKI_ENABLED: "true"
  LOKI_HOST: "loki"
  LOKI_PORT: "3100"
  LOKI_TLS_ENABLED: "true"
  CLUSTER_WORKERS: "0"
  MEMORY_LIMIT: "1024"
  CPU_LIMIT: "1.0"
  ROOT_USER_NAME: "admin"
  ROOT_USER_EMAIL: "<EMAIL>"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-config
  namespace: prs-production
data:
  NODE_ENV: "production"
  VITE_APP_ENVIRONMENT: "production"
  VITE_APP_API_URL: "https://api.your-domain.com"
  VITE_APP_UPLOAD_URL: "https://api.your-domain.com/upload"
  VITE_APP_WS_URL: "wss://api.your-domain.com/ws"
  VITE_APP_ENABLE_API_MOCKING: "false"
  VITE_APP_ENABLE_DEVTOOLS: "false"
  VITE_APP_CSP_ENABLED: "true"
  VITE_APP_LOKI_ENDPOINT: "https://monitoring.your-domain.com/loki"
  VITE_APP_PROMETHEUS_GATEWAY: "https://monitoring.your-domain.com/prometheus"
  VITE_APP_GRAFANA_URL: "https://monitoring.your-domain.com/grafana"
  VITE_APP_ENABLE_SERVICE_WORKER: "true"
  VITE_APP_CACHE_STRATEGY: "networkFirst"
  VITE_APP_OFFLINE_SUPPORT: "true"
  VITE_APP_LOG_LEVEL: "warn"
  VITE_APP_LOG_BATCH_SIZE: "50"
  VITE_APP_LOG_FLUSH_INTERVAL: "30000"
  VITE_APP_LOG_RETENTION_DAYS: "7"
  VITE_APP_APP_VERSION: "1.0.0"
  VITE_APP_CDN_URL: "https://cdn.your-domain.com"
  VITE_APP_STATIC_URL: "https://static.your-domain.com"
  VITE_APP_ENABLE_ANALYTICS: "true"
  VITE_APP_ENABLE_ERROR_REPORTING: "true"
  VITE_APP_ENABLE_PERFORMANCE_MONITORING: "true"
  VITE_APP_DEFAULT_THEME: "light"
  VITE_APP_ENABLE_DARK_MODE: "true"
  VITE_APP_DEFAULT_LOCALE: "en"
  VITE_APP_SUPPORTED_LOCALES: "en,es,fr"
