# Cloudflare Tunnel Configuration for PRS EC2
# This file defines how Cloudflare Tunnel routes traffic to your services

tunnel: your-tunnel-id
credentials-file: /etc/cloudflared/credentials.json

# Ingress rules - order matters!
ingress:
  # Main PRS Application
  - hostname: prs.stratpoint.io
    service: http://nginx:80
    originRequest:
      httpHostHeader: prs.stratpoint.io
      noTLSVerify: true

  # Grafana Monitoring Dashboard
  - hostname: grafana.stratpoint.io
    service: http://grafana:3000
    originRequest:
      httpHostHeader: grafana.stratpoint.io
      noTLSVerify: true

  # Adminer Database Management
  - hostname: adminer.stratpoint.io
    service: http://adminer:8080
    originRequest:
      httpHostHeader: adminer.stratpoint.io
      noTLSVerify: true

  # Portainer Container Management
  - hostname: portainer.stratpoint.io
    service: http://portainer:9000
    originRequest:
      httpHostHeader: portainer.stratpoint.io
      noTLSVerify: true

  # Catch-all rule (must be last)
  - service: http_status:404

# Tunnel configuration
warp-routing:
  enabled: false

# Logging
loglevel: info

# Metrics
metrics: 0.0.0.0:2000
