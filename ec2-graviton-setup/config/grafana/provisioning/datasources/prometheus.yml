# PRS Grafana Datasource Configuration
# Prometheus datasource for EC2 Graviton monitoring

apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090/prometheus
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: false
      prometheusType: Prometheus
      prometheusVersion: 2.40.0
      cacheLevel: 'High'
      disableMetricsLookup: false
      customQueryParameters: ''
      timeInterval: '30s'
    secureJsonData: {}
    version: 1
