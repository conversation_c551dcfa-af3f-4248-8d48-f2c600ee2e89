# Prometheus configuration for PRS Local Development
global:
  scrape_interval: 30s
  evaluation_interval: 30s
  external_labels:
    environment: 'local-development'
    instance: 'prs-local'

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/prometheus/metrics'

  # PRS Backend API
  - job_name: 'prs-backend'
    static_configs:
      - targets: ['backend:4000']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # PostgreSQL (if postgres_exporter is added)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  # Nginx (if nginx-prometheus-exporter is added)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s

# Rule files (for alerting - simplified for local development)
rule_files:
  # - "alert_rules.yml"

# Alerting configuration (disabled for local development)
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           - alertmanager:9093
