{"id": null, "title": "🏗️ PRS Production Replica Dashboard", "tags": ["prs", "production", "monitoring", "business"], "style": "dark", "timezone": "", "editable": true, "panels": [{"id": 1, "title": "📊 Business Metrics Overview", "type": "stat", "targets": [{"datasource": {"type": "postgres", "uid": "postgres"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  'Active Users' as metric, COUNT(*) as value FROM users WHERE status = 'active'\nUNION ALL\nSELECT \n  'Total Requisitions' as metric, COUNT(*) as value FROM requisitions\nUNION ALL\nSELECT \n  'Active Companies' as metric, COUNT(*) as value FROM companies\nUNION ALL\nSELECT \n  'Active Projects' as metric, COUNT(*) as value FROM projects\nUNION ALL\nSELECT \n  'Registered Suppliers' as metric, COUNT(*) as value FROM suppliers", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "short"}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "value", "graphMode": "none", "justifyMode": "auto"}, "pluginVersion": "8.0.0", "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "👥 Recent User Activity", "type": "table", "targets": [{"datasource": {"type": "postgres", "uid": "postgres"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  u.username,\n  u.first_name || ' ' || u.last_name as \"Full Name\",\n  r.name as \"Role\",\n  u.status as \"Status\",\n  u.created_at::date as \"Join Date\"\nFROM users u\nLEFT JOIN roles r ON u.role_id = r.id\nWHERE u.status = 'active'\nORDER BY u.created_at DESC\nLIMIT 8", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}]}}}, "options": {"showHeader": true}, "pluginVersion": "8.0.0", "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "📋 Requisition Status Breakdown", "type": "piechart", "targets": [{"datasource": {"type": "postgres", "uid": "postgres"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  COALESCE(status, 'Unknown') as status,\n  COUNT(*) as count\nFROM requisitions \nGROUP BY status\nORDER BY count DESC", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": []}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single"}, "legend": {"displayMode": "visible", "placement": "right"}}, "pluginVersion": "8.0.0", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}}, {"id": 4, "title": "🏢 Top Companies by Activity", "type": "table", "targets": [{"datasource": {"type": "postgres", "uid": "postgres"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  c.name as \"Company Name\",\n  c.category as \"Category\",\n  COUNT(DISTINCT p.id) as \"Projects\",\n  c.created_at::date as \"Registered\"\nFROM companies c\nLEFT JOIN projects p ON p.company_id = c.id\nGROUP BY c.id, c.name, c.category, c.created_at\nORDER BY COUNT(DISTINCT p.id) DESC, c.created_at DESC\nLIMIT 8", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}]}}}, "options": {"showHeader": true}, "pluginVersion": "8.0.0", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}}, {"id": 5, "title": "🎯 System Health Status", "type": "stat", "targets": [{"datasource": {"type": "postgres", "uid": "postgres"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  'Database Connection' as metric, \n  CASE WHEN COUNT(*) > 0 THEN 'Healthy' ELSE 'Error' END as value \nFROM users \nLIMIT 1", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"Healthy": {"color": "green", "index": 0, "text": "✅ Healthy"}, "Error": {"color": "red", "index": 1, "text": "❌ Error"}}, "type": "value"}], "thresholds": {"steps": [{"color": "green", "value": null}]}}}, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "gridPos": {"h": 4, "w": 24, "x": 0, "y": 17}}], "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "refresh": "1m", "schemaVersion": 27, "version": 1, "links": []}