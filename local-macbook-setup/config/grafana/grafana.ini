[server]
# The IP address to bind to, empty will bind to all interfaces
http_addr = 0.0.0.0

# The http port to use
http_port = 3000

# The public facing domain name used to access grafana from a browser
domain = localhost

# Redirect to correct domain if host header does not match domain
enforce_domain = false

# The full public facing url you use in browser, used for redirects and emails
root_url = https://localhost:8444/grafana/

# Serve <PERSON> from subpath specified in `root_url` setting. By default it is set to `false` for compatibility reasons.
serve_from_sub_path = true

[security]
# disable creation of admin user on first start of grafana
disable_initial_admin_creation = false

# default admin user, created on startup
admin_user = admin

# default admin password, can be changed before first start of grafana,  or in profile settings
admin_password = admin123

# used for signing
secret_key = SW2YcwTIb9zpOOhoPsMm

# disable gravatar profile images
disable_gravatar = true

# data source proxy whitelist (ip_or_domain:port separated by spaces)
allow_embedding = true

[users]
# disable user signup / registration
allow_sign_up = false

# Allow non admin users to create organizations
allow_org_create = false

# Set to true to automatically assign new users to the default organization (id 1)
auto_assign_org = true

# Set this value to automatically add new users to the provided organization (if auto_assign_org above is set to true)
auto_assign_org_id = 1

# Default role new users will be automatically assigned (if disabled above is set to true)
auto_assign_org_role = Viewer

[auth.anonymous]
# enable anonymous access
enabled = true

# specify organization name that should be used for unauthenticated users
org_name = Main Org.

# specify role for unauthenticated users
org_role = Viewer

[analytics]
# Server reporting, sends usage counters to stats.grafana.org every 24 hours.
reporting_enabled = false

# Set to false to disable all checks to https://grafana.net
check_for_updates = false

[log]
# Either "console", "file", "syslog". Default is console and file
mode = console

# Either "debug", "info", "warn", "error", "critical", default is "info"
level = info

[paths]
# Directory where grafana can store temp files, sessions, and the sqlite3 db (if that is used)
data = /var/lib/grafana

# Temporary files in `data` directory older than given duration will be removed
temp_data_lifetime = 24h

# Directory where grafana will automatically scan and look for plugins
plugins = /var/lib/grafana/plugins

# folder that contains provisioning config files that grafana will apply on startup and while running.
provisioning = /etc/grafana/provisioning

[dashboards]
# Path to the default home dashboard. If this value is empty, then Grafana uses StaticRootPath + "dashboards/home.json"
default_home_dashboard_path = /var/lib/grafana/dashboards/prs-production-replica.json

[panels]
# If set to true Grafana will allow script tags in text panels. Not recommended as it enable XSS vulnerabilities.
disable_sanitize_html = false

[feature_toggles]
# enable features, separated by spaces
enable =

[enterprise]
# Path to a valid Grafana Enterprise license.jwt file
license_path =
