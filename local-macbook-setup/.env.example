# PRS Local MacBook Development Configuration
# Copy this file to .env and customize for your local development

# Local Development Configuration
DOMAIN=localhost
HTTP_PORT=8080
HTTPS_PORT=8443
VERSION=latest

# Database Configuration
POSTGRES_DB=prs_local
POSTGRES_USER=prs_user
POSTGRES_PASSWORD=localdev123

# Application Security (Development Keys - Change for Production)
JWT_SECRET=local-development-jwt-secret-key-32-chars
ENCRYPTION_KEY=local-development-encryption-key
OTP_KEY=bG9jYWwtZGV2ZWxvcG1lbnQtb3RwLWtleS02NC1ieXRlcw==
PASS_SECRET=local-dev-pass-secret
BYPASS_OTP=true

# CORS Configuration (Local Development)
CORS_ORIGIN=https://localhost:8443,http://localhost:8080,http://localhost:3000,http://localhost:4000

# External API Configuration (Mock/Test endpoints)
CITYLAND_API_URL=https://cmd-test.free.beeceptor.com
CITYLAND_ACCOUNTING_URL=https://cityland-accounting.free.beeceptor.com

# Root User (Initial Admin)
ROOT_USER_NAME=admin
ROOT_USER_EMAIL=admin@localhost
ROOT_USER_PASSWORD=admin123

# Local Monitoring
GRAFANA_ADMIN_PASSWORD=admin123
LOG_LEVEL=debug

# Local Storage (Docker Volumes)
DATABASE_VOLUME=prs_local_database
UPLOADS_VOLUME=prs_local_uploads
LOGS_VOLUME=prs_local_logs

# SSL Configuration (Self-signed for local)
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem
SSL_DHPARAM_PATH=./ssl/dhparam.pem

# Email Configuration (Local Development - Optional)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=test@localhost
SMTP_PASSWORD=test
SMTP_FROM="PRS Local <test@localhost>"

# File Upload Configuration
MAX_FILE_SIZE=100m
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif

# Development Features
ENABLE_DEBUG_LOGS=true
ENABLE_HOT_RELOAD=true
ENABLE_CORS_ALL=true

# Container Resource Limits (Optimized for Local)
BACKEND_MEMORY_LIMIT=512m
FRONTEND_MEMORY_LIMIT=256m
POSTGRES_MEMORY_LIMIT=1g
GRAFANA_MEMORY_LIMIT=256m

# Timezone
TZ=Asia/Manila

# Container Update Policy
RESTART_POLICY=unless-stopped

# Local Development Network
NETWORK_NAME=prs_local_network
NETWORK_SUBNET=**********/16

# Development Mode Settings
NODE_ENV=development
VITE_APP_ENVIRONMENT=development
VITE_APP_ENABLE_DEVTOOLS=true

# Local Database Settings (Optimized for Development)
POSTGRES_SHARED_BUFFERS=64MB
POSTGRES_EFFECTIVE_CACHE_SIZE=256MB
POSTGRES_WORK_MEM=2MB
POSTGRES_MAINTENANCE_WORK_MEM=16MB

# Backup Configuration (Local)
BACKUP_ENABLED=false
BACKUP_RETENTION_DAYS=7

# Monitoring Configuration (Simplified for Local)
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
NODE_EXPORTER_ENABLED=false
CADVISOR_ENABLED=false

# Local Development Flags
SKIP_SSL_VERIFICATION=true
ENABLE_MOCK_APIS=true
ENABLE_TEST_DATA=true
