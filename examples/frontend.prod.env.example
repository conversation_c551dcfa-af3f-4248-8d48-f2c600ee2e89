# Frontend Production Configuration
# Copy this file to frontend.prod.env and customize for your environment

# Application Configuration
NODE_ENV=production
VITE_APP_ENVIRONMENT=production

# API Configuration
VITE_APP_API_URL=https://api.your-domain.com
VITE_APP_UPLOAD_URL=https://api.your-domain.com/upload
VITE_APP_WS_URL=wss://api.your-domain.com/ws

# Security Configuration
VITE_APP_ENABLE_API_MOCKING=false
VITE_APP_ENABLE_DEVTOOLS=false
VITE_APP_CSP_ENABLED=true

# Monitoring Configuration
VITE_APP_LOKI_ENDPOINT=https://monitoring.your-domain.com/loki
VITE_APP_PROMETHEUS_GATEWAY=https://monitoring.your-domain.com/prometheus
VITE_APP_GRAFANA_URL=https://monitoring.your-domain.com/grafana

# Performance Configuration
VITE_APP_ENABLE_SERVICE_WORKER=true
VITE_APP_CACHE_STRATEGY=networkFirst
VITE_APP_OFFLINE_SUPPORT=true

# Logging Configuration
VITE_APP_LOG_LEVEL=warn
VITE_APP_LOG_BATCH_SIZE=50
VITE_APP_LOG_FLUSH_INTERVAL=30000
VITE_APP_LOG_RETENTION_DAYS=7

# Application Metadata
VITE_APP_APP_VERSION=1.0.0
VITE_APP_BUILD_ID=${BUILD_ID:-production}
VITE_APP_DEPLOY_DATE=${DEPLOY_DATE:-$(date -u +%Y-%m-%dT%H:%M:%SZ)}

# CDN Configuration (Optional)
VITE_APP_CDN_URL=https://cdn.your-domain.com
VITE_APP_STATIC_URL=https://static.your-domain.com

# Feature Flags
VITE_APP_ENABLE_ANALYTICS=true
VITE_APP_ENABLE_ERROR_REPORTING=true
VITE_APP_ENABLE_PERFORMANCE_MONITORING=true

# Theme Configuration
VITE_APP_DEFAULT_THEME=light
VITE_APP_ENABLE_DARK_MODE=true

# Localization
VITE_APP_DEFAULT_LOCALE=en
VITE_APP_SUPPORTED_LOCALES=en,es,fr
