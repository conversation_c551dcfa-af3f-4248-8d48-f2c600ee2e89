# Backend Production Configuration
# Copy this file to backend.prod.env and customize for your environment

# Application Configuration
NODE_ENV=production
PORT=4000
HOST=0.0.0.0
LOG_LEVEL=info
ENABLE_REQUEST_LOGS=true
DISABLE_DEBUG_ROUTES=true
ENABLE_SWAGGER=false

# Security Configuration (Secrets will be loaded from Docker secrets)
BYPASS_OTP=false
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# Database Configuration
POSTGRES_HOST=postgres-primary
POSTGRES_DB=prs_production
POSTGRES_PORT=5432
POSTGRES_USER=prs_user
POSTGRES_SSL_MODE=require

# Database Pool Configuration
POOL_MIN=5
POOL_MAX=20
POOL_ACQUIRE=60000
POOL_IDLE=10000
POOL_EVICTION=30000

# Redis Configuration
REDIS_HOST=redis-cluster
REDIS_PORT=6379
REDIS_TLS_ENABLED=true
REDIS_CLUSTER_ENABLED=true

# MinIO Configuration
MINIO_ENDPOINT=minio-cluster
MINIO_PORT=9000
MINIO_USE_SSL=true
MINIO_BUCKET=prs-uploads-prod

# External API Configuration (Update with actual URLs)
CITYLAND_API_URL=https://api.cityland.gov
CITYLAND_ACCOUNTING_URL=https://accounting.cityland.gov

# Department Association
ASSOCIATION_DEPARTMENT_CODE=10

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL=true

# CORS Configuration
CORS_ORIGIN=https://your-domain.com,https://api.your-domain.com
CORS_CREDENTIALS=true

# Monitoring Configuration
PROMETHEUS_ENABLED=true
LOKI_ENABLED=true
LOKI_HOST=loki
LOKI_PORT=3100
LOKI_TLS_ENABLED=true

# Performance Configuration
CLUSTER_WORKERS=0  # Use all CPU cores
MEMORY_LIMIT=1024
CPU_LIMIT=1.0

# Root User Configuration
ROOT_USER_NAME=admin
ROOT_USER_EMAIL=<EMAIL>
