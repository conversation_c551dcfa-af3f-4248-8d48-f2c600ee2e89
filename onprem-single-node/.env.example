# PRS On-Premises Single Node Configuration (LAN/VPN Access Only)
# Copy this file to .env and customize for your internal network

# Internal Network Configuration
DOMAIN=*************  # Your server's internal IP address
# Or use internal hostname: DOMAIN=prs.company.local
VERSION=latest

# Database Configuration
POSTGRES_DB=prs_onprem
POSTGRES_USER=prs_user
POSTGRES_PASSWORD=SecurePassword123!

# Application Security
JWT_SECRET=your-jwt-secret-key-minimum-32-characters-long
ENCRYPTION_KEY=your-encryption-key-32-chars
OTP_KEY=your-otp-key-base64-encoded
PASS_SECRET=your-password-secret-key
BYPASS_OTP=false

# CORS Configuration (Internal Network)
CORS_ORIGIN=https://*************,http://*************,https://prs.company.local
# Add your internal network ranges as needed

# External API Configuration (if accessible from internal network)
CITYLAND_API_URL=https://api.cityland.gov
CITYLAND_ACCOUNTING_URL=https://accounting.cityland.gov

# Root User (Initial Admin)
ROOT_USER_NAME=admin
ROOT_USER_EMAIL=<EMAIL>
ROOT_USER_PASSWORD=AdminPassword123!

# Internal Monitoring
GRAFANA_ADMIN_PASSWORD=GrafanaAdmin123!
LOG_LEVEL=info

# Storage Paths (NAS mounted at /mnt/nas)
NAS_PATH=/mnt/nas
DATABASE_PATH=/mnt/nas/database
UPLOADS_PATH=/mnt/nas/uploads
BACKUPS_PATH=/mnt/nas/backups
LOGS_PATH=/mnt/nas/logs

# NAS Configuration (Internal Network)
NAS_SERVER=*************  # Your NAS server internal IP
# Or: NAS_SERVER=nas.company.local
NAS_SHARE=volume1/prs
NAS_MOUNT_TYPE=nfs
# For SMB/CIFS (Windows shares):
# NAS_MOUNT_TYPE=cifs
# NAS_USERNAME=your-nas-user
# NAS_PASSWORD=your-nas-password
# NAS_DOMAIN=WORKGROUP

# SSL Configuration (Internal Network)
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem
SSL_DHPARAM_PATH=./ssl/dhparam.pem
# Note: Uses self-signed certificates for internal access

# Email Configuration (Internal SMTP - Optional)
SMTP_HOST=mail.company.local  # Internal mail server
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=EmailPassword123!
SMTP_FROM=PRS System <<EMAIL>>

# Internal Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION_KEY=backup-encryption-key-32-chars-long

# Basic Authentication for Admin Tools
ADMIN_USER=admin
ADMIN_PASSWORD=AdminTools123!

# Application Features (Internal Network)
ENABLE_REGISTRATION=false  # Usually disabled for internal systems
ENABLE_EMAIL_VERIFICATION=false  # Not needed for internal deployment
ENABLE_PASSWORD_RESET=true
SESSION_TIMEOUT=3600  # 1 hour (can be longer for internal use)
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900  # 15 minutes

# File Upload Configuration
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif

# Database Pool Settings (Internal Network)
POSTGRES_POOL_MIN=2
POSTGRES_POOL_MAX=10
POSTGRES_POOL_ACQUIRE=30000
POSTGRES_POOL_IDLE=10000

# Rate Limiting (Relaxed for Internal Network)
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=200  # Higher limit for internal use
RATE_LIMIT_SKIP_SUCCESSFUL=true

# Logging Configuration
LOG_TO_FILE=true
LOG_MAX_SIZE=10m
LOG_MAX_FILES=10
LOG_LEVEL_CONSOLE=info
LOG_LEVEL_FILE=debug

# Internal Network Settings
NODE_ENV=production
ENABLE_DEBUG_ROUTES=false
ENABLE_SWAGGER=false  # Can be enabled for internal development

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# Container Resource Limits
BACKEND_MEMORY_LIMIT=1g
FRONTEND_MEMORY_LIMIT=256m
POSTGRES_MEMORY_LIMIT=2g
GRAFANA_MEMORY_LIMIT=512m

# Timezone
TZ=Asia/Manila

# Container Update Policy
RESTART_POLICY=unless-stopped

# Internal Network Security
ENABLE_HTTPS_REDIRECT=true
ENABLE_SECURITY_HEADERS=true
ENABLE_RATE_LIMITING=true

# Internal DNS Configuration (Optional)
INTERNAL_DOMAIN=company.local
HOSTNAME=prs

# VPN Configuration (Optional - for documentation)
# VPN_NETWORK=10.0.0.0/8  # Your VPN network range
# VPN_GATEWAY=********    # VPN server gateway

# Network Interface (Optional - specify if multiple interfaces)
# BIND_INTERFACE=eth0  # Network interface to bind services

# Internal Certificate Authority (Optional)
# If you have an internal CA, you can specify it here
# INTERNAL_CA_CERT=/path/to/internal-ca.crt
# INTERNAL_CA_KEY=/path/to/internal-ca.key

# Monitoring Configuration (Internal)
ENABLE_METRICS=true
ENABLE_HEALTH_CHECKS=true
METRICS_RETENTION_DAYS=30

# Internal Proxy Configuration
PROXY_TIMEOUT=60s
PROXY_BUFFER_SIZE=64k
PROXY_CACHE_SIZE=100m

# Development/Testing (for internal development)
ENABLE_DEVELOPMENT_MODE=false
ENABLE_API_DOCUMENTATION=false  # Set to true if needed for internal API docs

# Compliance and Auditing (Internal)
ENABLE_AUDIT_LOGS=true
AUDIT_LOG_RETENTION_DAYS=90
ENABLE_LOGIN_LOGGING=true

# Internal Network Performance Tuning
DATABASE_SHARED_BUFFERS=256MB
DATABASE_EFFECTIVE_CACHE_SIZE=1GB
DATABASE_WORK_MEM=4MB
DATABASE_MAINTENANCE_WORK_MEM=64MB

# File System Optimization
FS_CACHE_SIZE=512m
FS_MAX_OPEN_FILES=1024

# Internal Service Discovery (if using)
# SERVICE_DISCOVERY_ENABLED=false
# CONSUL_HOST=consul.company.local
# CONSUL_PORT=8500

# Internal Load Balancing (if multiple servers in future)
# ENABLE_LOAD_BALANCING=false
# UPSTREAM_SERVERS=*************,*************

# Notification Configuration (Internal)
NOTIFICATION_METHOD=email  # email, slack, or none
SLACK_WEBHOOK_URL=  # Internal Slack webhook if used
NOTIFICATION_EMAIL=<EMAIL>

# Internal Integration Settings
LDAP_ENABLED=false  # Enable if using Active Directory/LDAP
# LDAP_SERVER=ldap.company.local
# LDAP_PORT=389
# LDAP_BIND_DN=cn=admin,dc=company,dc=local
# LDAP_BIND_PASSWORD=ldap_password
# LDAP_BASE_DN=dc=company,dc=local

# Single Sign-On (Internal)
SSO_ENABLED=false  # Enable if using internal SSO
# SSO_PROVIDER=saml  # or oidc
# SSO_ENDPOINT=https://sso.company.local
# SSO_CLIENT_ID=prs-application
# SSO_CLIENT_SECRET=sso_client_secret
