global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'prs-production'
    environment: 'production'

rule_files:
  - "/etc/prometheus/alerts/*.yml"
  - "/etc/prometheus/recording-rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Backend application
  - job_name: 'prs-backend'
    static_configs:
      - targets: ['backend:4000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # MinIO
  - job_name: 'minio'
    static_configs:
      - targets: ['minio-cluster:9000']
    metrics_path: '/minio/metrics'

  # Node Exporter
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # cAdvisor
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

recording_rules:
  - record: prs:request_rate_5m
    expr: rate(http_requests_total[5m])
  
  - record: prs:error_rate_5m
    expr: rate(http_requests_total{status=~"5.."}[5m])
  
  - record: prs:response_time_95th
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))

  - record: prs:database_connections_used
    expr: pg_stat_database_numbackends / pg_settings_max_connections * 100

  - record: prs:redis_memory_used
    expr: redis_memory_used_bytes / redis_config_maxmemory * 100

  - record: prs:disk_usage
    expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100

  - record: prs:cpu_usage
    expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)

  - record: prs:memory_usage
    expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100
